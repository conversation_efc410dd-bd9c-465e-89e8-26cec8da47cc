<idea-plugin url="https://github.com/acejump/AceJump">
  <name>AceJump</name>
  <id>AceJump</id>

  <description><![CDATA[
    AceJump allows you to quickly navigate the caret to any position visible in the editor.
    See a demo of <a href="https://www.youtube.com/watch?v=8cgy8ITtsJE">AceJump in action</a>!
    Simply hit "ctrl+;", type a character, then type the matching character to Ace Jump. ]]>
  </description>

  <depends>com.intellij.modules.platform</depends>

  <category>Navigation</category>
  <vendor url="https://github.com/acejump/AceJump">AceJump</vendor>

  <extensions defaultExtensionNs="com.intellij">
    <applicationService serviceImplementation="org.acejump.config.AceConfig"/>
    <applicationConfigurable groupId="tools" displayName="AceJump"
                             instance="org.acejump.config.AceConfigurable"
                             id="preferences.AceConfigurable" dynamic="true"/>

    <editorActionHandler action="EditorEscape" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$Reset"
                         id="AceHandlerEscape"/>
    <editorActionHandler action="EditorBackSpace" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$ClearSearch"
                         id="AceHandlerBackSpace"/>
    <editorActionHandler action="EditorStartNewLine" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$SelectBackward"
                         id="AceHandlerStartNewLine"/>
    <editorActionHandler action="EditorEnter" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$SelectForward"
                         id="AceHandlerEnter"/>
    <editorActionHandler action="EditorTab" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$ScrollToNextScreenful"
                         id="AceHandlerTab"/>
    <editorActionHandler action="EditorUnindentSelection" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$ScrollToPreviousScreenful"
                         id="AceHandlerUnindentSelection"/>
    <editorActionHandler action="EditorUp" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$SearchLineStarts"
                         id="AceHandlerUp"/>
    <editorActionHandler action="EditorLeft" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$SearchLineIndents"
                         id="AceHandlerLeft"/>
    <editorActionHandler action="EditorLineStart" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$SearchLineIndents"
                         id="AceHandlerLineStart"/>
    <editorActionHandler action="EditorRight" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$SearchLineEnds"
                         id="AceHandlerRight"/>
    <editorActionHandler action="EditorLineEnd" order="first"
                         implementationClass="org.acejump.action.AceEditorAction$SearchLineEnds"
                         id="AceHandlerLineEnd"/>

  </extensions>

  <actions>
    <action id="AceAction"
            class="org.acejump.action.AceAction$ActivateOrCycleMode"
            text="Activate / Cycle AceJump Mode">
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="ctrl SEMICOLON"/>
      <keyboard-shortcut keymap="Mac OS X 10.5+" first-keystroke="ctrl SEMICOLON"/>
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl SEMICOLON"/>
    </action>
    <action id="AceReverseAction"
            class="org.acejump.action.AceAction$ActivateOrReverseCycleMode"
            text="Activate / Reverse Cycle AceJump Mode"/>
    <action id="AceForwardAction"
            class="org.acejump.action.AceAction$ToggleForwardJumpMode"
            text="Start AceJump in Jump After Caret Mode"/>
    <action id="AceBackwardAction"
            class="org.acejump.action.AceAction$ToggleBackwardJumpMode"
            text="Start AceJump in Jump Before Caret Mode"/>
    <action id="AceWordStartAction"
            class="org.acejump.action.AceAction$ToggleJumpMode"
            text="Start AceJump in Jump Mode"/>
    <action id="AceWordEndAction"
            class="org.acejump.action.AceAction$ToggleJumpEndMode"
            text="Start AceJump in Jump End Mode"/>
    <action id="AceTargetAction"
            class="org.acejump.action.AceAction$ToggleTargetMode"
            text="Start AceJump in Target Mode">
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="ctrl alt SEMICOLON"/>
      <keyboard-shortcut keymap="Mac OS X 10.5+" first-keystroke="ctrl alt SEMICOLON"/>
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt SEMICOLON"/>
    </action>
    <action id="AceDeclarationAction"
            class="org.acejump.action.AceAction$ToggleDeclarationMode"
            text="Start AceJump in Declaration Mode"/>
    <action id="AceLineAction"
            class="org.acejump.action.AceAction$StartAllLineMarksMode"
            text="Start AceJump in All Line Marks Mode">
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="ctrl shift SEMICOLON"/>
      <keyboard-shortcut keymap="Mac OS X 10.5+" first-keystroke="ctrl shift SEMICOLON"/>
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl shift SEMICOLON"/>
    </action>
    <action id="AceLineStartsAction"
            class="org.acejump.action.AceAction$StartAllLineStartsMode"
            text="Start AceJump in All Line Starts Mode"/>
    <action id="AceLineEndsAction"
            class="org.acejump.action.AceAction$StartAllLineEndsMode"
            text="Start AceJump in All Line Ends Mode"/>
    <action id="AceLineIndentsAction"
            class="org.acejump.action.AceAction$StartAllLineIndentsMode"
            text="Start AceJump in All Line Indents Mode"/>
    <action id="AceWordAction"
            class="org.acejump.action.AceAction$StartAllWordsMode"
            text="Start AceJump in All Words Mode"/>
    <action id="AceWordForwardAction"
            class="org.acejump.action.AceAction$StartAllWordsForwardMode"
            text="Start AceJump in All Words After Caret Mode"/>
    <action id="AceWordBackwardsAction"
            class="org.acejump.action.AceAction$StartAllWordsBackwardsMode"
            text="Start AceJump in All Words Before Caret Mode"/>
    <action id="AceSelectRangeAction"
            class="org.acejump.action.AceAction$StartSelectRangeMode"
            text="Start AceJump in Select Range Mode">
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="ctrl alt shift SEMICOLON"/>
      <keyboard-shortcut keymap="Mac OS X 10.5+" first-keystroke="ctrl alt shift SEMICOLON"/>
      <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt shift SEMICOLON"/>
    </action>
  </actions>
</idea-plugin>
