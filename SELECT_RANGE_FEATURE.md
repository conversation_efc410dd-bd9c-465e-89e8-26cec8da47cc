# AceJump SELECT_RANGE Mode Feature

## Overview

This document describes the new SELECT_RANGE mode added to AceJump, which allows users to select text from their original cursor position to any target position using AceJump's navigation system.

## Feature Description

The SELECT_RANGE mode provides the following functionality:

1. **Records Original Position**: When the mode is activated, it records the current cursor position
2. **Word Search by Default**: Automatically starts with ALL_WORDS search pattern to show all visible words
3. **Range Selection**: When jumping to a target, selects all text from the original position to the target position
4. **Cursor Positioning**: Places the cursor at the end of the selection

## Usage

### Keyboard Shortcut
- **Default**: `Ctrl+Alt+Shift+;` (Windows/Linux)
- **Mac**: `Ctrl+Alt+Shift+;`

### How to Use
1. Position your cursor at the starting point of the text you want to select
2. Press `Ctrl+Alt+Shift+;` to activate SELECT_RANGE mode
3. All words in the visible area will be tagged with jump markers
4. Type the tag characters corresponding to your target word
5. Press the final tag character to jump and select the range

### Example Workflow
```
Original text: "function calculateSum(a, b) { return a + b; }"
                ^cursor here

1. Press Ctrl+Alt+Shift+; (SELECT_RANGE mode activated)
2. Words are tagged: [f]unction [c]alculateSum([a], [b]) { [r]eturn [a] + [b]; }
3. Type 'r' to target "return"
4. Result: "function calculateSum(a, b) { return" is selected
           cursor is positioned after "return"
```

## Implementation Details

### New Components Added

1. **JumpMode.SELECT_RANGE**: New enum value in JumpMode
2. **AceAction.StartSelectRangeMode**: New action class that:
   - Activates SELECT_RANGE mode
   - Starts ALL_WORDS regex search
3. **Enhanced TagJumper**: Modified to handle range selection logic
4. **Enhanced Session**: Records original cursor position when entering SELECT_RANGE mode

### Configuration

- **Color**: New `selectRangeModeColor` setting (default: #FF6B9D - pink)
- **Keyboard Shortcut**: Registered in plugin.xml as "AceSelectRangeAction"

### Code Changes Summary

1. **JumpMode.kt**: Added SELECT_RANGE enum value with documentation
2. **AceSettings.kt**: Added selectRangeModeColor configuration
3. **AceConfig.kt**: Added selectRangeModeColor accessor
4. **TagJumper.kt**: Enhanced to support range selection from original position
5. **Session.kt**: Added originalCaretOffset tracking
6. **AceAction.kt**: Added StartSelectRangeMode action class
7. **plugin.xml**: Registered new action with keyboard shortcut
8. **AceTest.kt**: Added test cases for the new functionality

## Benefits

1. **Efficient Text Selection**: Quickly select large blocks of text without dragging
2. **Precise Targeting**: Use AceJump's precise word targeting for selection endpoints
3. **Consistent UX**: Follows AceJump's existing interaction patterns
4. **Keyboard-Only**: Fully keyboard-driven workflow for power users

## Future Enhancements

Potential improvements that could be added:

1. **Multiple Selection**: Support for multiple range selections
2. **Line-based Selection**: Alternative mode for selecting entire lines
3. **Configurable Search Patterns**: Allow users to choose different search patterns
4. **Visual Feedback**: Enhanced visual indicators for the original position
5. **Undo Integration**: Better integration with IDE's undo system

## Testing

The implementation includes comprehensive test cases:

- Basic range selection functionality
- Word search integration
- Cursor positioning verification
- Edge case handling

## Compatibility

This feature is compatible with:
- All IntelliJ Platform IDEs (2025.1+)
- Existing AceJump configurations
- All keyboard layouts supported by AceJump
- Multi-editor scenarios
