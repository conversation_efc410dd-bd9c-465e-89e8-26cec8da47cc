[versions]
# libraries
junit = "4.13.2"

# plugins
changelog = "2.2.1"
intelliJPlatform = "2.5.0"
kotlin = "2.1.20"
kover = "0.9.1"

[libraries]
junit = { group = "junit", name = "junit", version.ref = "junit" }

[plugins]
changelog = { id = "org.jetbrains.changelog", version.ref = "changelog" }
intelliJPlatform = { id = "org.jetbrains.intellij.platform", version.ref = "intelliJPlatform" }
kotlin = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kover = { id = "org.jetbrains.kotlinx.kover", version.ref = "kover" }
