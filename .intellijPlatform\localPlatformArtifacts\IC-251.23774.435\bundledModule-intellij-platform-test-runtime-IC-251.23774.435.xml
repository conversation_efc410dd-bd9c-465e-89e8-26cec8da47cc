<ivy-module version="2.0">
  <info organisation="bundledModule" module="intellij-platform-test-runtime" revision="IC-251.23774.435"/>
  <configurations>
    <conf name="default" visibility="public"/>
  </configurations>
  <publications>
    <artifact name="app-client" ext="jar" conf="default" url="lib"/>
    <artifact name="idea_rt" ext="jar" conf="default" url="lib"/>
    <artifact name="stats" ext="jar" conf="default" url="lib"/>
    <artifact name="testFramework" ext="jar" conf="default" url="lib"/>
    <artifact name="intellij.grid.core.impl" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.grid.csv.core.impl" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.grid.impl" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.grid" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.grid.types" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.ide.startup.importSettings" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.idea.customization.base" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.kotlin.onboarding-promoter" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.libraries.compose.foundation.desktop" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.libraries.ktor.client.cio" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.libraries.ktor.client" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.libraries.microba" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.libraries.skiko" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.bookmarks.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.bookmarks.frontend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.clouds" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.collaborationTools" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.compose" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.coverage.agent" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.coverage" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.debugger.impl.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.debugger.impl.frontend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.diagnostic.freezeAnalyzer" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.diagnostic.freezes" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.editor.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.editor.frontend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.editor" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.execution.dashboard" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.execution.serviceView" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.frontend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.ide.newUiOnboarding" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.ide.newUsersOnboarding" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.images.backend.svg" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.images.copyright" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.inline.completion" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.jewel.foundation" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.jewel.ideLafBridge" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.jewel.markdown.core" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.jewel.markdown.extension.gfmAlerts" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.jewel.markdown.extension.gfmTables" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.jewel.markdown.ideLafBridgeStyling" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.jewel.ui" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.kernel.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.lvcs.impl" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.navbar.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.navbar.frontend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.navbar" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.navbar.monolith" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.progress.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.project.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.recentFiles.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.recentFiles.frontend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.recentFiles" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.registry.cloud" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.rpc.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.searchEverywhere.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.searchEverywhere.frontend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.searchEverywhere" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.settings.local" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.smRunner.vcs" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.tips" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.dvcs.impl" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.dvcs.impl.shared" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.impl.backend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.impl.exec" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.impl.frontend" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.impl" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.impl.lang.actions" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.impl.lang" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.impl.shared" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.platform.vcs.log.impl" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.settingsSync.core" ext="jar" conf="default" url="lib/modules"/>
    <artifact name="intellij.xml.xmlbeans" ext="jar" conf="default" url="lib/modules"/>
  </publications>
  <dependencies/>
</ivy-module>