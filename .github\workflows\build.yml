name: CI

on:
  push:
    paths:
      - '**.kt'
      - '**.kts'

jobs:
  build:

    runs-on: macos-latest

    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v1
        with:
          java-version: 17
      - name: Build with Gradle
        run: |
          git submodule update --init --recursive
          ./gradlew test buildPlugin --stacktrace
